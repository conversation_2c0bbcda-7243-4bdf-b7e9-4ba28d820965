<% content_for :title, "User Management" %>

<div class="modern-admin-container">
  <header class="modern-admin-header">
    <div>
      <h1>User Management</h1>
      <p>Manage user profiles, approvals, and tiers.</p>
    </div>
    <%= link_to "Referral Codes", admin_referral_codes_path, class: "button" %>
  </header>

  <div class="modern-admin-card">
    <!-- Filter Buttons -->
    <div class="modern-filter-buttons">
      <%= link_to admin_subscriptions_path, class: "filter-button #{'active' if @status_filter.blank?}" do %>
        All <span class="count"><%= @status_counts[:total] %></span>
      <% end %>
      <%= link_to admin_subscriptions_path(status: 'free'), class: "filter-button #{'active' if @status_filter == 'free'}" do %>
        Free <span class="count"><%= @status_counts[:free] %></span>
      <% end %>
      <%= link_to admin_subscriptions_path(status: 'premium'), class: "filter-button #{'active' if @status_filter == 'premium'}" do %>
        Premium <span class="count"><%= @status_counts[:premium] %></span>
      <% end %>
      <%= link_to admin_subscriptions_path(status: 'vanguard'), class: "filter-button #{'active' if @status_filter == 'vanguard'}" do %>
        Vanguard <span class="count"><%= @status_counts[:vanguard] %></span>
      <% end %>
      <%= link_to admin_subscriptions_path(status: 'approved'), class: "filter-button #{'active' if @status_filter == 'approved'}" do %>
        Approved <span class="count"><%= @status_counts[:approved] %></span>
      <% end %>
      <%= link_to admin_subscriptions_path(status: 'pending'), class: "filter-button #{'active' if @status_filter == 'pending'}" do %>
        Pending <span class="count"><%= @status_counts[:pending] %></span>
      <% end %>
    </div>

    <% if @users && @users.any? %>
      <div class="admin-card-grid">
      <% @users.each do |user| %>
        <div class="admin-user-card">
          <!-- Card Header -->
          <div class="admin-user-card-header">
            <div class="user-info">
              <!-- Simple circle avatar without image -->
              <div class="user-avatar">
                <%= user.user_profile&.first_name&.first&.upcase || user.email.first.upcase %>
              </div>
              <div class="user-details">
                <h3>
                  <% if user.user_profile&.first_name.present? %>
                    <%= user.user_profile.first_name %> <%= user.user_profile.last_name %>
                  <% else %>
                    <%= user.email.split('@').first %>
                  <% end %>
                </h3>
                <p><%= user.email %></p>
                <p style="font-size: 0.75rem; color: #6b7280;">ID: <%= user.id %></p>
              </div>
            </div>
            <!-- Status Badge -->
            <div class="status-badge <%= user.approved? ? 'status-approved' : 'status-pending' %>">
              <%= user.approved? ? 'Approved' : 'Pending' %>
            </div>
          </div>

          <!-- Card Content -->
          <div class="admin-user-card-content">
            <% if user.user_profile&.bio.present? %>
              <p class="user-bio"><%= truncate(user.user_profile.bio, length: 100) %></p>
            <% else %>
              <p class="user-bio">No bio</p>
            <% end %>
            <p class="user-contact">
              <% if user.user_profile&.phone.present? %>
                <%= user.user_profile.formatted_phone %>
              <% else %>
                No phone
              <% end %>
            </p>
          </div>

          <!-- Card Footer -->
          <div class="admin-user-card-footer">
            <div>
              <span class="tier-badge"><%= user.subscription_tier.humanize %></span>
            </div>
            <div class="card-actions">
              <!-- Approval Actions -->
              <% if user.approved? %>
                <%= form_with model: user, url: update_approval_admin_subscription_path(user), method: :patch, local: true, html: { style: 'display: contents;' } do |form| %>
                  <%= button_tag type: :submit, name: "approval_action", value: "disapprove", class: "action-button decline", title: "Disapprove User" do %>
                    <%= heroicon "x-mark", variant: :outline, options: { class: "hero-icon" } %>
                  <% end %>
                <% end %>
              <% else %>
                <%= form_with model: user, url: update_approval_admin_subscription_path(user), method: :patch, local: true, html: { style: 'display: contents;' } do |form| %>
                  <%= button_tag type: :submit, name: "approval_action", value: "approve", class: "action-button approve", title: "Approve User" do %>
                    <%= heroicon "check", variant: :outline, options: { class: "hero-icon" } %>
                  <% end %>
                <% end %>
              <% end %>

              <!-- More Actions Dropdown -->
              <div class="dropdown">
                <button class="action-icon-btn action-icon-gray" title="More Actions">
                  <%= heroicon "ellipsis-vertical", variant: :outline, options: { class: "hero-icon" } %>
                </button>
                <div class="dropdown-menu">
                  <!-- Tier Management -->
                  <%= form_with url: update_tier_admin_subscription_path(user), method: :patch, local: true do |form| %>
                    <%= form.hidden_field :subscription_tier, value: 'free' %>
                    <%= form.submit "Set Free", style: "all: unset; display: block; width: 100%; padding: 0.5rem 1rem; font-size: 0.875rem; cursor: pointer; color: #{user.subscription_tier == 'free' ? '#9ca3af' : '#374151'}; background: none; border: none;" %>
                  <% end %>
                  <%= form_with url: update_tier_admin_subscription_path(user), method: :patch, local: true do |form| %>
                    <%= form.hidden_field :subscription_tier, value: 'premium' %>
                    <%= form.submit "Set Premium", style: "all: unset; display: block; width: 100%; padding: 0.5rem 1rem; font-size: 0.875rem; cursor: pointer; color: #{user.subscription_tier == 'premium' ? '#9ca3af' : '#374151'}; background: none; border: none;" %>
                  <% end %>
                  <%= form_with url: update_tier_admin_subscription_path(user), method: :patch, local: true do |form| %>
                    <%= form.hidden_field :subscription_tier, value: 'vanguard' %>
                    <%= form.submit "Set Vanguard", style: "all: unset; display: block; width: 100%; padding: 0.5rem 1rem; font-size: 0.875rem; cursor: pointer; color: #{user.subscription_tier == 'vanguard' ? '#9ca3af' : '#374151'}; background: none; border: none;" %>
                  <% end %>
                  <hr style="margin: 0.5rem 0; border: none; border-top: 1px solid #e5e7eb;">
                  <a href="#" class="danger">Delete User</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% end %>
      </div>

      <!-- Pagination -->
      <% if @pagy.pages > 1 %>
        <div style="text-align: center; margin-top: 20px;">
          <%= raw pagy_nav(@pagy) %>
        </div>
      <% end %>
    <% else %>
      <p style="text-align: center; color: #6b7280; margin: 2rem 0;">No users to display.</p>
    <% end %>
  </div>
</div>