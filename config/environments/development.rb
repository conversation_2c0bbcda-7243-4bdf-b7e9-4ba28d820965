require "active_support/core_ext/integer/time"

Rails.application.configure do
  # Settings specified here will take precedence over those in config/application.rb.

  # In the development environment your application's code is reloaded any time
  # it changes. This slows down response time but is perfect for development
  # since you don't have to restart the web server when you make code changes.
  config.cache_classes = false

  # Do not eager load code on boot.
  config.eager_load = false

  # Show full error reports.
  config.consider_all_requests_local = true

  # Enable server timing
  config.server_timing = true

  # Enable/disable caching. By default caching is disabled.
  # Run rails dev:cache to toggle caching.
  if Rails.root.join("tmp/caching-dev.txt").exist?
    config.action_controller.perform_caching = true
    config.action_controller.enable_fragment_cache_logging = true

    config.cache_store = :memory_store
    config.public_file_server.headers = {
      "Cache-Control" => "public, max-age=#{2.days.to_i}"
    }
  else
    config.action_controller.perform_caching = false

    config.cache_store = :null_store
  end

  # Store uploaded files on the local file system (see config/storage.yml for options).
  #config.active_storage.service = :local
  # NOTE: Service now specified per-attachment in models (Rails best practice)
  # Removed global service config - services are explicitly defined per attachment

  # Set log level to debug to see full email content in logs
  config.log_level = :debug

  # Don't care if the mailer can't send.
  config.action_mailer.raise_delivery_errors = false

  config.action_mailer.perform_caching = false

  # Print deprecation notices to the Rails logger.
  config.active_support.deprecation = :log

  # Raise exceptions for disallowed deprecations.
  config.active_support.disallowed_deprecation = :raise

  # Tell Active Support which deprecation messages to disallow.
  config.active_support.disallowed_deprecation_warnings = []

  # Raise an error on page load if there are pending migrations.
  config.active_record.migration_error = :page_load

  # Highlight code that triggered database queries in logs.
  config.active_record.verbose_query_logs = true

  # Suppress logger output for asset requests.
    
  config.assets.compile = true 
  # added after working commit
  config.assets.debug = true 
  config.assets.digest = false 
  # used when deploy on render didn't work
  #config.assets.quiet = true
  #config.assets.check_precompiled_asset = false

  # Raises error for missing translations.
  # config.i18n.raise_on_missing_translations = true

  # Annotate rendered view with file names.
  # config.action_view.annotate_rendered_view_with_filenames = true

  # Uncomment if you wish to allow Action Cable access from any origin.
  # config.action_cable.disable_request_forgery_protection = true
  
  # Allow web console from LAN in development
  config.web_console.allowed_ips = ['127.0.0.1', '::1', '***********/16', '10.0.0.0/8', '**********/12']

  # Action Mailer configuration for development
  config.action_mailer.default_url_options = { host: 'localhost', port: 5000 }
  config.action_mailer.delivery_method = :test  # Prevents actual email sending
  config.action_mailer.perform_deliveries = true  # Ensures mailer logging occurs
  config.action_mailer.raise_delivery_errors = false  # Don't crash on email errors
  config.action_mailer.preview_path = "#{Rails.root}/test/mailers/previews"
  
  # Set the queue_adapter for Active Job to :good_job
  config.active_job.queue_adapter = :good_job
end
