# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_08_11_140000) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_trgm"
  enable_extension "plpgsql"
  enable_extension "unaccent"

  create_table "active_admin_comments", force: :cascade do |t|
    t.string "namespace"
    t.text "body"
    t.string "resource_type"
    t.bigint "resource_id"
    t.string "author_type"
    t.bigint "author_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["author_type", "author_id"], name: "index_active_admin_comments_on_author"
    t.index ["namespace"], name: "index_active_admin_comments_on_namespace"
    t.index ["resource_type", "resource_id"], name: "index_active_admin_comments_on_resource"
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "admin_users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_admin_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_admin_users_on_reset_password_token", unique: true
  end

  create_table "cache_entries", force: :cascade do |t|
    t.string "key", null: false
    t.text "value"
    t.datetime "expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["expires_at"], name: "index_cache_entries_on_expires_at"
    t.index ["key"], name: "index_cache_entries_on_key", unique: true
  end

  create_table "connection_requests", force: :cascade do |t|
    t.bigint "inviter_id", null: false
    t.bigint "invitee_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "message"
    t.bigint "project_id"
    t.index ["invitee_id"], name: "index_connection_requests_on_invitee_id"
    t.index ["inviter_id"], name: "index_connection_requests_on_inviter_id"
    t.index ["project_id"], name: "index_connection_requests_on_project_id"
  end

  create_table "good_job_batches", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "description"
    t.jsonb "serialized_properties"
    t.text "on_finish"
    t.text "on_success"
    t.text "on_discard"
    t.text "callback_queue_name"
    t.integer "callback_priority"
    t.datetime "enqueued_at"
    t.datetime "discarded_at"
    t.datetime "finished_at"
    t.datetime "jobs_finished_at"
  end

  create_table "good_job_executions", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.uuid "active_job_id", null: false
    t.text "job_class"
    t.text "queue_name"
    t.jsonb "serialized_params"
    t.datetime "scheduled_at"
    t.datetime "finished_at"
    t.text "error"
    t.integer "error_event", limit: 2
    t.text "error_backtrace", array: true
    t.uuid "process_id"
    t.interval "duration"
    t.index ["active_job_id", "created_at"], name: "index_good_job_executions_on_active_job_id_and_created_at"
    t.index ["process_id", "created_at"], name: "index_good_job_executions_on_process_id_and_created_at"
  end

  create_table "good_job_processes", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "state"
    t.integer "lock_type", limit: 2
  end

  create_table "good_job_settings", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "key"
    t.jsonb "value"
    t.index ["key"], name: "index_good_job_settings_on_key", unique: true
  end

  create_table "good_jobs", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.text "queue_name"
    t.integer "priority"
    t.jsonb "serialized_params"
    t.datetime "scheduled_at"
    t.datetime "performed_at"
    t.datetime "finished_at"
    t.text "error"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.uuid "active_job_id"
    t.text "concurrency_key"
    t.text "cron_key"
    t.uuid "retried_good_job_id"
    t.datetime "cron_at"
    t.uuid "batch_id"
    t.uuid "batch_callback_id"
    t.boolean "is_discrete"
    t.integer "executions_count"
    t.text "job_class"
    t.integer "error_event", limit: 2
    t.text "labels", array: true
    t.uuid "locked_by_id"
    t.datetime "locked_at"
    t.index ["active_job_id", "created_at"], name: "index_good_jobs_on_active_job_id_and_created_at"
    t.index ["batch_callback_id"], name: "index_good_jobs_on_batch_callback_id", where: "(batch_callback_id IS NOT NULL)"
    t.index ["batch_id"], name: "index_good_jobs_on_batch_id", where: "(batch_id IS NOT NULL)"
    t.index ["concurrency_key", "created_at"], name: "index_good_jobs_on_concurrency_key_and_created_at"
    t.index ["concurrency_key"], name: "index_good_jobs_on_concurrency_key_when_unfinished", where: "(finished_at IS NULL)"
    t.index ["cron_key", "created_at"], name: "index_good_jobs_on_cron_key_and_created_at_cond", where: "(cron_key IS NOT NULL)"
    t.index ["cron_key", "cron_at"], name: "index_good_jobs_on_cron_key_and_cron_at_cond", unique: true, where: "(cron_key IS NOT NULL)"
    t.index ["finished_at"], name: "index_good_jobs_jobs_on_finished_at", where: "((retried_good_job_id IS NULL) AND (finished_at IS NOT NULL))"
    t.index ["labels"], name: "index_good_jobs_on_labels", where: "(labels IS NOT NULL)", using: :gin
    t.index ["locked_by_id"], name: "index_good_jobs_on_locked_by_id", where: "(locked_by_id IS NOT NULL)"
    t.index ["priority", "created_at"], name: "index_good_job_jobs_for_candidate_lookup", where: "(finished_at IS NULL)"
    t.index ["priority", "created_at"], name: "index_good_jobs_jobs_on_priority_created_at_when_unfinished", order: { priority: "DESC NULLS LAST" }, where: "(finished_at IS NULL)"
    t.index ["priority", "scheduled_at"], name: "index_good_jobs_on_priority_scheduled_at_unfinished_unlocked", where: "((finished_at IS NULL) AND (locked_by_id IS NULL))"
    t.index ["queue_name", "scheduled_at"], name: "index_good_jobs_on_queue_name_and_scheduled_at", where: "(finished_at IS NULL)"
    t.index ["scheduled_at"], name: "index_good_jobs_on_scheduled_at", where: "(finished_at IS NULL)"
  end

  create_table "network_connections", force: :cascade do |t|
    t.bigint "inviter_id", null: false
    t.bigint "invitee_id", null: false
    t.integer "level", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_friend", default: false
    t.boolean "is_accepted", default: false
    t.index ["invitee_id", "inviter_id"], name: "index_network_connections_on_invitee_id_and_inviter_id"
    t.index ["invitee_id"], name: "index_network_connections_on_invitee_id"
    t.index ["inviter_id", "invitee_id"], name: "index_network_connections_on_inviter_id_and_invitee_id"
    t.index ["inviter_id"], name: "index_network_connections_on_inviter_id"
    t.index ["is_accepted"], name: "index_network_connections_on_is_accepted"
  end

  create_table "project_auths", force: :cascade do |t|
    t.integer "access_level", default: 1
    t.bigint "user_id", null: false
    t.bigint "project_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["project_id"], name: "index_project_auths_on_project_id"
    t.index ["user_id", "project_id"], name: "index_project_auths_on_user_id_and_project_id", unique: true
    t.index ["user_id"], name: "index_project_auths_on_user_id"
  end

  create_table "project_shares", force: :cascade do |t|
    t.bigint "project_id", null: false
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["project_id"], name: "index_project_shares_on_project_id"
    t.index ["user_id"], name: "index_project_shares_on_user_id"
  end

  create_table "projects", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "title"
    t.string "location"
    t.text "short_description"
    t.text "summary"
    t.boolean "public_visible"
    t.boolean "summary_only"
    t.boolean "full_access"
    t.integer "visibility"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "semi_public", default: false
    t.boolean "friends_only", default: false
    t.integer "price_value"
    t.string "price_text"
    t.text "full_description"
    t.integer "land_area"
    t.string "area_unit"
    t.boolean "network_only", default: false
    t.string "price_currency"
    t.integer "category"
    t.integer "subcategory"
    t.float "latitude"
    t.float "longitude"
    t.string "country"
    t.string "country_code"
    t.integer "project_type"
    t.string "commission"
    t.string "commission_type"
    t.boolean "project_status", default: true
    t.boolean "approved", default: false
    t.datetime "first_published_at"
    t.index ["category"], name: "index_projects_on_category"
    t.index ["latitude", "longitude"], name: "index_projects_on_latitude_and_longitude"
    t.index ["network_only"], name: "index_projects_on_network_only"
    t.index ["project_status"], name: "index_projects_on_project_status"
    t.index ["public_visible"], name: "index_projects_on_public_visible"
    t.index ["semi_public"], name: "index_projects_on_semi_public"
    t.index ["summary"], name: "index_projects_on_summary", opclass: :gin_trgm_ops, using: :gin
    t.index ["user_id"], name: "index_projects_on_user_id"
  end

  create_table "referral_codes", force: :cascade do |t|
    t.string "code", null: false
    t.bigint "created_by_id", null: false
    t.integer "status", default: 0, null: false
    t.integer "tier_upgrade_to", default: 1
    t.integer "duration_months", default: 1
    t.integer "max_uses", default: 1
    t.integer "current_uses", default: 0
    t.datetime "expires_at"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["code"], name: "index_referral_codes_on_code", unique: true
    t.index ["created_by_id"], name: "index_referral_codes_on_created_by_id"
    t.index ["status", "expires_at"], name: "index_referral_codes_on_status_and_expires_at"
    t.check_constraint "current_uses <= max_uses", name: "current_uses_lte_max_uses"
  end

  create_table "uploads", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "status", default: "pending", null: false
    t.string "original_filename", null: false
    t.string "content_type", null: false
    t.bigint "file_size", null: false
    t.string "temp_file_path"
    t.string "s3_key"
    t.text "error_message"
    t.string "target_model"
    t.integer "target_id"
    t.string "signed_id_token", null: false
    t.integer "progress_percentage", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "target_type"
    t.index ["created_at"], name: "index_uploads_on_created_at"
    t.index ["signed_id_token"], name: "index_uploads_on_signed_id_token", unique: true
    t.index ["status"], name: "index_uploads_on_status"
    t.index ["target_model", "target_id"], name: "index_uploads_on_target_model_and_target_id"
    t.index ["target_type", "target_id"], name: "index_uploads_on_target_type_and_target_id"
    t.index ["user_id", "status"], name: "index_uploads_on_user_id_and_status"
    t.index ["user_id"], name: "index_uploads_on_user_id"
  end

  create_table "user_profiles", force: :cascade do |t|
    t.string "first_name"
    t.string "last_name"
    t.string "email"
    t.text "bio"
    t.string "phone"
    t.string "location"
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "city"
    t.string "country"
    t.string "default_language", default: "en"
    t.boolean "profile_completed", default: false
    t.index ["user_id"], name: "index_user_profiles_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string "unconfirmed_email"
    t.integer "failed_attempts", default: 0, null: false
    t.string "unlock_token"
    t.datetime "locked_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "invitation_token"
    t.datetime "invitation_created_at"
    t.datetime "invitation_sent_at"
    t.datetime "invitation_accepted_at"
    t.integer "invitation_limit"
    t.string "invited_by_type"
    t.bigint "invited_by_id"
    t.integer "invitations_count", default: 0
    t.integer "connections_count", default: 0, null: false
    t.integer "role", default: 0
    t.integer "subscription_tier", default: 0, null: false
    t.datetime "subscription_expires_at"
    t.string "referral_code"
    t.boolean "approved", default: false, null: false
    t.index ["approved"], name: "index_users_on_approved"
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["created_at", "id"], name: "index_users_on_created_at_desc_id_desc", order: :desc
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["invitation_token"], name: "index_users_on_invitation_token", unique: true
    t.index ["invited_by_id"], name: "index_users_on_invited_by_id"
    t.index ["invited_by_type", "invited_by_id"], name: "index_users_on_invited_by"
    t.index ["referral_code"], name: "index_users_on_referral_code", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["role"], name: "index_users_on_role"
    t.index ["unlock_token"], name: "index_users_on_unlock_token", unique: true
  end

  create_table "wants", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.text "summary"
    t.integer "price_min"
    t.integer "price_max"
    t.integer "want_type"
    t.integer "category"
    t.integer "subcategory"
    t.string "place"
    t.integer "radius"
    t.string "country"
    t.string "country_code"
    t.string "price_currency"
    t.boolean "notification"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "description"
    t.index ["category", "place"], name: "index_wants_on_category_and_place"
    t.index ["category"], name: "index_wants_on_category"
    t.index ["notification"], name: "index_wants_on_notification"
    t.index ["place"], name: "index_wants_on_place"
    t.index ["price_max"], name: "index_wants_on_price_max"
    t.index ["price_min", "price_max"], name: "index_wants_on_price_min_and_price_max"
    t.index ["price_min"], name: "index_wants_on_price_min"
    t.index ["subcategory", "place"], name: "index_wants_on_subcategory_and_place"
    t.index ["subcategory"], name: "index_wants_on_subcategory"
    t.index ["user_id"], name: "index_wants_on_user_id"
    t.index ["want_type"], name: "index_wants_on_want_type"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "connection_requests", "projects"
  add_foreign_key "connection_requests", "users", column: "invitee_id"
  add_foreign_key "connection_requests", "users", column: "inviter_id"
  add_foreign_key "network_connections", "users", column: "invitee_id"
  add_foreign_key "network_connections", "users", column: "inviter_id"
  add_foreign_key "project_auths", "projects"
  add_foreign_key "project_auths", "users"
  add_foreign_key "project_shares", "projects"
  add_foreign_key "project_shares", "users"
  add_foreign_key "projects", "users"
  add_foreign_key "referral_codes", "users", column: "created_by_id"
  add_foreign_key "uploads", "users"
  add_foreign_key "user_profiles", "users"
  add_foreign_key "wants", "users"
end
