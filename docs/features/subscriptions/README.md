# Subscription Tier System Implementation

**Status**: ✅ Complete (Phase 1)  
**Rails Version**: 7.0.8+  
**Complexity**: 3-4 points  

## Overview

The subscription tier system provides three-tier access control (free/premium/pilot) with Rails-native enum-based architecture, tier detection logic, and basic referral code system. This is a simplified Phase 1 implementation with no external payment integration.

## Core Features

### 1. Subscription Tiers

- **Free Tier** (default): Limited access, cannot create projects
- **Premium Tier**: Full access with expiration tracking  
- **Pilot Tier**: Permanent full access without expiration

### 2. Referral Code System

- Admins can create referral codes for tier upgrades
- Usage tracking with max uses and expiration dates
- Race condition protection via database locking
- Automatic tier assignment upon redemption

### 3. Access Control Integration

- ActionPolicy integration for feature restrictions
- Automatic expiration processing via background jobs
- Admin management interface for tier assignments

## Database Schema

### User Table Additions
```sql
-- New columns added to users table
subscription_tier INTEGER DEFAULT 0 NOT NULL  -- enum: {free: 0, premium: 1, pilot: 2}
subscription_expires_at DATETIME              -- expiration for premium users
referral_code STRING                          -- unique referral code per user

-- Indexes for performance
INDEX idx_users_subscription_tier (subscription_tier)
INDEX idx_users_referral_code (referral_code) UNIQUE
```

### ReferralCode Table
```sql
CREATE TABLE referral_codes (
  id BIGINT PRIMARY KEY,
  code STRING NOT NULL,
  created_by_id BIGINT NOT NULL,
  status INTEGER DEFAULT 0 NOT NULL,         -- enum: {active: 0, expired: 1, used_up: 2, disabled: 3}
  tier_upgrade_to INTEGER DEFAULT 1,         -- enum: {premium: 1, pilot: 2}
  duration_months INTEGER DEFAULT 1,
  max_uses INTEGER DEFAULT 1,
  current_uses INTEGER DEFAULT 0,
  expires_at DATETIME,
  description TEXT,
  
  CONSTRAINT current_uses_lte_max_uses CHECK (current_uses <= max_uses)
);

-- Indexes
INDEX idx_referral_codes_code (code) UNIQUE
INDEX idx_referral_codes_status_expires (status, expires_at)
```

## Model Implementation

### User Model

```ruby
# app/models/user.rb
enum subscription_tier: { free: 0, premium: 1, pilot: 2 }, _prefix: :tier

def active_subscription?
  return true if tier_pilot? # Pilot never expires
  return false if tier_free?
  tier_premium? && subscription_expires_at.present? && subscription_expires_at > Time.current
end

def subscription_active?
  return true if tier_pilot? # Pilot never expires
  return false if tier_free?
  subscription_expires_at.present? && subscription_expires_at > Time.current
end

def generate_referral_code!
  loop do
    self.referral_code = SecureRandom.alphanumeric(8).upcase
    break unless User.exists?(referral_code: referral_code)
  end
  save!
end
```

### ReferralCode Model

```ruby
# app/models/referral_code.rb
class ReferralCode < ApplicationRecord
  belongs_to :created_by, class_name: 'User'
  
  enum status: { active: 0, expired: 1, used_up: 2, disabled: 3 }
  enum tier_upgrade_to: { premium: 1, pilot: 2 }
  
  validates :code, presence: true, uniqueness: true, length: { in: 6..12 }
  validates :max_uses, presence: true, numericality: { greater_than: 0 }
  validates :duration_months, presence: true, numericality: { greater_than: 0 }
  
  scope :available, -> { where(status: :active).where('expires_at > ? OR expires_at IS NULL', Time.current) }
  
  def redeem!(user)
    ReferralCode.transaction do
      lock! # Prevent race conditions
      
      raise "Code expired" if expires_at&.past?
      raise "Code usage limit reached" if current_uses >= max_uses
      raise "Code not active" unless active?
      raise "User already has premium access" if user.active_subscription?
      
      # Apply tier upgrade
      expiry_date = duration_months.months.from_now
      user.update!(
        subscription_tier: tier_upgrade_to,
        subscription_expires_at: tier_upgrade_to == 'pilot' ? nil : expiry_date
      )
      
      # Update usage tracking
      increment!(:current_uses)
      update!(status: :used_up) if current_uses >= max_uses
    end
    true
  end
end
```

## ActionPolicy Integration

### Application Policy

```ruby
# app/policies/application_policy.rb
def premium_feature_access?
  user.present? && user.active_subscription?
end

def pilot_feature_access?
  user.present? && user.tier_pilot?
end
```

### Project Policy

```ruby
# app/policies/project_policy.rb
def create?
  premium_feature_access?
end

def upload_files?
  premium_feature_access?
end
```

## Manual Administration

### Subscription Management

All subscription and referral code management is handled manually by administrators:
- Admins manually update user tiers via admin interface
- Expired premium users retain access until manually downgraded by admin
- Referral codes remain active until manually disabled by admin
- No background processing or automatic cleanup occurs

## Admin Interface

### Routes
```ruby
namespace :admin do
  resources :subscriptions, only: [:index] do
    member do
      patch :update_tier
    end
  end
  resources :referral_codes
end

# User referral code redemption
post 'redeem_referral_code', to: 'referral_codes#redeem'
get 'redeem_code', to: 'referral_codes#new', as: :new_referral_code_redemption
```

### Controllers
- `Admin::SubscriptionsController` - User tier management
- `Admin::ReferralCodesController` - Referral code management
- `ReferralCodesController` - User-facing referral code redemption

### Admin Views
- **Subscription Management** (`/admin/subscriptions`)
  - User tier overview with statistics dashboard
  - Individual user tier assignment and expiration management
  - Modal-based tier updates with validation
- **Referral Code Management** (`/admin/referral_codes`)
  - Create new referral codes with usage limits
  - Track code usage and expiration status
  - View detailed code analytics and update settings

### Admin Navigation
Admin links added to main navigation:
- "Subscriptions" → `/admin/subscriptions`
- "Referral Codes" → `/admin/referral_codes`

## Testing

### Key Test Coverage

1. **User Model Tests** (`spec/models/user_spec.rb`)
   - Subscription tier enum behavior
   - Expiration logic for premium/pilot users
   - Referral code generation

2. **ReferralCode Model Tests** (`spec/models/referral_code_spec.rb`)
   - Validation and enum behavior
   - Redemption logic with race condition protection
   - Usage tracking and status transitions

3. **Policy Tests** (`spec/policies/project_policy_spec.rb`)
   - Subscription tier access controls
   - Premium feature restrictions

4. **Job Tests** (`spec/jobs/subscription_expiration_job_spec.rb`)
   - Expiration processing
   - Proper tier downgrading

## Security Considerations

1. **Race Condition Protection**: Referral code redemption uses database locking
2. **Database Constraints**: Enforces current_uses <= max_uses
3. **Admin Access**: All admin controllers verify admin role
4. **Validation**: Comprehensive model validations prevent invalid states

## API Usage Examples

### Creating a Referral Code (Admin)
```ruby
code = ReferralCode.create!(
  code: "WELCOME2024",
  created_by: admin_user,
  tier_upgrade_to: :premium,
  duration_months: 3,
  max_uses: 10,
  expires_at: 1.month.from_now,
  description: "Welcome promotion"
)
```

### Redeeming a Code
```ruby
code = ReferralCode.find_by(code: "WELCOME2024")
code.redeem!(user) # Upgrades user to premium for 3 months
```

### Checking Access
```ruby
# In controllers
authorize! project, to: :create?  # Will check premium_feature_access?

# Direct access
user.active_subscription?  # true for premium/pilot users
user.tier_pilot?           # true only for pilot users
```

## Migration Commands

```bash
# Apply migrations
bin/rails db:migrate

# Run tests
bundle exec rspec spec/models/user_spec.rb
bundle exec rspec spec/models/referral_code_spec.rb
bundle exec rspec spec/policies/project_policy_spec.rb
bundle exec rspec spec/jobs/subscription_expiration_job_spec.rb
```

## User Interface Components

### Subscription Status Components
- **`_subscription_status.html.erb`** - Full subscription status card for dashboards
  - Shows current tier, expiration status, and feature access
  - Upgrade prompts for free users
  - Renewal options for expired premium users
  - Includes referral code redemption modal
- **`_subscription_badge.html.erb`** - Compact tier badge for navigation
  - Shows current tier with icon
  - Displays expiration warnings
  - Quick upgrade button for free users

### Referral Code Redemption
- **Redemption Interface** (`/redeem_code`)
  - Standalone page for entering referral codes
  - Current subscription status display
  - Premium benefits preview
  - User-friendly error handling
- **Modal Integration** - Embedded in subscription status components
- **Controller Handling** - Comprehensive error messages and success feedback

### UI Integration Examples
```erb
<!-- Dashboard subscription status -->
<%= render 'shared/subscription_status' %>

<!-- Navigation tier badge -->
<%= render 'shared/subscription_badge' %>

<!-- Direct redemption link -->
<%= link_to "Redeem Code", new_referral_code_redemption_path %>
```

## Future Enhancements (Not in Phase 1)

- Stripe payment integration
- Email notifications for expiration
- Usage analytics and reporting  
- Advanced fraud protection
- Audit trail for tier changes
- User self-service subscription management
- Mobile-responsive admin interface improvements

## Troubleshooting

### Common Issues

1. **Tests failing with Slovak error messages**: Error message expectations updated to check for presence rather than exact text
2. **Race conditions in code redemption**: Database locking prevents concurrent redemptions
3. **Expired users still have access**: Manual administration - admins must manually downgrade expired users via admin interface

### Debugging Commands

```ruby
# Check subscription status
User.group(:subscription_tier).count

# Find expired premium users
User.tier_premium.where('subscription_expires_at < ?', Time.current)

# Check referral code usage
ReferralCode.where(status: :active).sum(:current_uses)
```